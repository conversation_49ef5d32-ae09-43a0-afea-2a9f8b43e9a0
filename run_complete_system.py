#!/usr/bin/env python3
"""
Complete System Runner
Starts all components of the GitHub Webhook MongoDB Integration System
"""

import os
import sys
import time
import signal
import subprocess
import multiprocessing
from pathlib import Path

class SystemRunner:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.processes = []
        self.running = False
        
    def check_mongodb(self):
        """Check if MongoDB is running"""
        print("🔍 Checking MongoDB connection...")
        try:
            from database.connection import MongoDBConnection
            db = MongoDBConnection()
            if db.test_connection():
                print("✅ MongoDB is running and accessible")
                return True
            else:
                print("❌ MongoDB connection failed")
                return False
        except Exception as e:
            print(f"❌ MongoDB check failed: {e}")
            return False
    
    def start_webhook_receiver(self):
        """Start the webhook receiver"""
        print("🚀 Starting webhook receiver...")
        try:
            process = subprocess.Popen([
                sys.executable, "webhook_receiver.py"
            ], cwd=self.base_dir)
            self.processes.append(("Webhook Receiver", process))
            
            # Wait a moment for it to start
            time.sleep(3)
            
            # Check if it's running
            if process.poll() is None:
                print("✅ Webhook receiver started successfully")
                return True
            else:
                print("❌ Webhook receiver failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Failed to start webhook receiver: {e}")
            return False
    
    def start_data_display(self):
        """Start the data display system"""
        print("📊 Starting data display system...")
        try:
            process = subprocess.Popen([
                sys.executable, "data_display.py"
            ], cwd=self.base_dir)
            self.processes.append(("Data Display", process))
            
            # Wait a moment for it to start
            time.sleep(2)
            
            # Check if it's running
            if process.poll() is None:
                print("✅ Data display system started successfully")
                return True
            else:
                print("❌ Data display system failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Failed to start data display system: {e}")
            return False
    
    def start_tunnel(self):
        """Start LocalTunnel for public access"""
        print("🌐 Starting LocalTunnel for public access...")
        try:
            process = subprocess.Popen([
                "npx", "localtunnel", "--port", "5000"
            ], cwd=self.base_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            self.processes.append(("LocalTunnel", process))
            
            # Wait for tunnel to establish
            time.sleep(5)
            
            if process.poll() is None:
                print("✅ LocalTunnel started successfully")
                print("   Check the terminal output for the public URL")
                return True
            else:
                print("⚠️  LocalTunnel may not be available (optional)")
                return False
                
        except Exception as e:
            print(f"⚠️  LocalTunnel not available: {e}")
            return False
    
    def display_status(self):
        """Display system status"""
        print("\n" + "=" * 60)
        print("📊 SYSTEM STATUS")
        print("=" * 60)
        
        # Check webhook receiver
        try:
            import requests
            response = requests.get("http://localhost:5000/health", timeout=5)
            if response.status_code == 200:
                print("✅ Webhook Receiver: Running on http://localhost:5000")
                print("   📡 Webhook endpoint: http://localhost:5000/webhook")
                print("   🔍 Health check: http://localhost:5000/health")
                print("   📋 Recent data: http://localhost:5000/recent")
            else:
                print("❌ Webhook Receiver: Not responding")
        except:
            print("❌ Webhook Receiver: Not accessible")
        
        # Check processes
        active_processes = []
        for name, process in self.processes:
            if process.poll() is None:
                active_processes.append(name)
            else:
                print(f"❌ {name}: Stopped")
        
        if active_processes:
            print(f"✅ Active processes: {', '.join(active_processes)}")
        
        print("\n🔧 Usage:")
        print("   • View webhook data: http://localhost:5000/recent")
        print("   • Test webhook: npm run test-webhook")
        print("   • Monitor logs: Check the data display terminal")
        
        print("\n⚠️  To stop the system: Press Ctrl+C")
    
    def test_webhook(self):
        """Test the webhook system"""
        print("\n🧪 Testing webhook system...")
        try:
            # Wait for systems to be ready
            time.sleep(2)
            
            # Run webhook test
            result = subprocess.run([
                "node", "action-repo/webhook-test.js"
            ], cwd=self.base_dir, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ Webhook test successful!")
                print("   Check the data display terminal for new data")
            else:
                print("❌ Webhook test failed")
                print(f"   Error: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Webhook test failed: {e}")
    
    def cleanup(self):
        """Clean up processes"""
        print("\n🛑 Shutting down system...")
        
        for name, process in self.processes:
            if process.poll() is None:
                print(f"   Stopping {name}...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
        
        print("✅ System shutdown complete")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.running = False
        self.cleanup()
        sys.exit(0)
    
    def run_system(self):
        """Run the complete system"""
        print("🚀 GitHub Webhook MongoDB Integration System")
        print("=" * 60)
        
        # Set up signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # Check prerequisites
        if not self.check_mongodb():
            print("\n❌ MongoDB is not running. Please start MongoDB first:")
            print("   mongod")
            return False
        
        # Start components
        webhook_started = self.start_webhook_receiver()
        if not webhook_started:
            print("❌ Failed to start webhook receiver")
            return False
        
        display_started = self.start_data_display()
        if not display_started:
            print("⚠️  Data display system failed to start")
        
        # Start tunnel (optional)
        self.start_tunnel()
        
        # Display status
        self.display_status()
        
        # Test the system
        self.test_webhook()
        
        # Keep running
        self.running = True
        print("\n🎉 System is running! Press Ctrl+C to stop.")
        
        try:
            while self.running:
                time.sleep(1)
                
                # Check if critical processes are still running
                webhook_process = next((p for name, p in self.processes if name == "Webhook Receiver"), None)
                if webhook_process and webhook_process.poll() is not None:
                    print("❌ Webhook receiver stopped unexpectedly")
                    break
                    
        except KeyboardInterrupt:
            pass
        
        self.cleanup()
        return True

def main():
    """Main entry point"""
    runner = SystemRunner()
    success = runner.run_system()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
