#!/usr/bin/env python3
"""
System Validation Script
Validates the complete GitHub Webhook MongoDB Integration System
"""

import os
import sys
import time
import json
import requests
import subprocess
from pathlib import Path
from datetime import datetime, timezone

class SystemValidator:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.webhook_url = "http://localhost:5000"
        self.test_results = []
        
    def log_test(self, test_name, success, message=""):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if message:
            print(f"    {message}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
    
    def test_file_structure(self):
        """Test if all required files exist"""
        print("\n📁 Testing File Structure...")
        
        required_files = [
            "webhook_receiver.py",
            "data_display.py",
            "start_system.py",
            "requirements.txt",
            "package.json",
            ".env.example",
            "README.md",
            "models/repository_data.py",
            "database/connection.py",
            "action-repo/package.json",
            "action-repo/webhook-test.js",
            "action-repo/.github/workflows/webhook-trigger.yml"
        ]
        
        all_exist = True
        for file_path in required_files:
            full_path = self.base_dir / file_path
            exists = full_path.exists()
            self.log_test(f"File exists: {file_path}", exists)
            if not exists:
                all_exist = False
                
        return all_exist
    
    def test_python_dependencies(self):
        """Test Python dependencies"""
        print("\n🐍 Testing Python Dependencies...")
        
        required_modules = [
            ("flask", "flask"),
            ("pymongo", "pymongo"),
            ("python-dotenv", "dotenv"),
            ("requests", "requests")
        ]

        all_imported = True
        for module_name, import_name in required_modules:
            try:
                __import__(import_name)
                self.log_test(f"Python module: {module_name}", True)
            except ImportError:
                self.log_test(f"Python module: {module_name}", False, "Module not installed")
                all_imported = False
                
        return all_imported
    
    def test_mongodb_connection(self):
        """Test MongoDB connection"""
        print("\n🍃 Testing MongoDB Connection...")
        
        try:
            from database.connection import MongoDBConnection
            db = MongoDBConnection()
            
            # Test connection
            connected = db.test_connection()
            self.log_test("MongoDB connection", connected)
            
            if connected:
                # Test database operations
                from models.repository_data import RepositoryDataModel
                repo_model = RepositoryDataModel()
                
                # Test data insertion
                test_data = {
                    "author": "test_user",
                    "pushed_to": "test-repo:main",
                    "on": datetime.now(timezone.utc).isoformat(),
                    "sample": "Test commit message"
                }
                
                try:
                    result = repo_model.insert_data(test_data)
                    self.log_test("MongoDB data insertion", bool(result))
                    
                    # Clean up test data
                    if result:
                        repo_model.collection.delete_one({"_id": result})
                        
                except Exception as e:
                    self.log_test("MongoDB data insertion", False, str(e))
                    
            return connected
            
        except Exception as e:
            self.log_test("MongoDB connection", False, str(e))
            return False
    
    def test_webhook_receiver(self):
        """Test webhook receiver"""
        print("\n🎣 Testing Webhook Receiver...")
        
        # Check if webhook receiver is running
        try:
            response = requests.get(f"{self.webhook_url}/health", timeout=5)
            running = response.status_code == 200
            self.log_test("Webhook receiver running", running)
            
            if running:
                # Test webhook endpoint
                test_payload = {
                    "ref": "refs/heads/main",
                    "repository": {
                        "name": "test-repo",
                        "full_name": "test-user/test-repo"
                    },
                    "pusher": {"name": "test-user"},
                    "head_commit": {
                        "message": "Test webhook validation",
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "author": {"username": "test-user"}
                    }
                }
                
                try:
                    webhook_response = requests.post(
                        f"{self.webhook_url}/webhook",
                        json=test_payload,
                        headers={
                            "Content-Type": "application/json",
                            "X-GitHub-Event": "push"
                        },
                        timeout=10
                    )
                    
                    webhook_works = webhook_response.status_code == 200
                    self.log_test("Webhook endpoint", webhook_works)
                    
                    if webhook_works:
                        # Test recent data endpoint
                        recent_response = requests.get(f"{self.webhook_url}/recent", timeout=5)
                        recent_works = recent_response.status_code == 200
                        self.log_test("Recent data endpoint", recent_works)
                        
                        return recent_works
                    
                except Exception as e:
                    self.log_test("Webhook endpoint", False, str(e))
                    
            return running
            
        except Exception as e:
            self.log_test("Webhook receiver running", False, str(e))
            return False
    
    def test_data_display(self):
        """Test data display system"""
        print("\n📊 Testing Data Display System...")
        
        try:
            from data_display import DataDisplaySystem
            display_system = DataDisplaySystem()
            
            # Test initialization
            self.log_test("Data display initialization", True)
            
            # Test database connection
            try:
                recent_data = display_system.repo_model.get_recent_data(limit=1)
                self.log_test("Data display database access", True)
                return True
            except Exception as e:
                self.log_test("Data display database access", False, str(e))
                return False
                
        except Exception as e:
            self.log_test("Data display initialization", False, str(e))
            return False
    
    def test_github_actions(self):
        """Test GitHub Actions configuration"""
        print("\n⚙️ Testing GitHub Actions Configuration...")
        
        workflow_file = self.base_dir / "action-repo" / ".github" / "workflows" / "webhook-trigger.yml"
        
        if workflow_file.exists():
            self.log_test("GitHub Actions workflow file", True)
            
            # Check workflow content
            try:
                with open(workflow_file, 'r') as f:
                    content = f.read()
                    
                has_push_trigger = "push:" in content
                has_pr_trigger = "pull_request:" in content
                has_webhook_test = "webhook" in content.lower()
                
                self.log_test("Workflow has push trigger", has_push_trigger)
                self.log_test("Workflow has PR trigger", has_pr_trigger)
                self.log_test("Workflow has webhook test", has_webhook_test)
                
                return has_push_trigger and has_pr_trigger
                
            except Exception as e:
                self.log_test("Workflow file readable", False, str(e))
                return False
        else:
            self.log_test("GitHub Actions workflow file", False, "File not found")
            return False
    
    def test_webhook_test_utility(self):
        """Test webhook testing utility"""
        print("\n🧪 Testing Webhook Test Utility...")
        
        webhook_test_file = self.base_dir / "action-repo" / "webhook-test.js"
        
        if webhook_test_file.exists():
            self.log_test("Webhook test utility exists", True)
            
            # Test if Node.js dependencies are installed
            node_modules = self.base_dir / "action-repo" / "node_modules"
            if node_modules.exists():
                self.log_test("Node.js dependencies installed", True)
                return True
            else:
                self.log_test("Node.js dependencies installed", False, "Run 'npm install' in action-repo")
                return False
        else:
            self.log_test("Webhook test utility exists", False)
            return False
    
    def generate_report(self):
        """Generate validation report"""
        print("\n" + "=" * 60)
        print("📋 SYSTEM VALIDATION REPORT")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['message']}")
        
        # Save report to file
        report_file = self.base_dir / "validation_report.json"
        with open(report_file, 'w') as f:
            json.dump({
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "summary": {
                    "total": total_tests,
                    "passed": passed_tests,
                    "failed": failed_tests,
                    "success_rate": (passed_tests/total_tests)*100
                },
                "results": self.test_results
            }, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        return failed_tests == 0
    
    def run_validation(self):
        """Run complete system validation"""
        print("🔍 GitHub Webhook MongoDB Integration System Validation")
        print("=" * 60)
        
        # Run all tests
        self.test_file_structure()
        self.test_python_dependencies()
        self.test_mongodb_connection()
        self.test_webhook_receiver()
        self.test_data_display()
        self.test_github_actions()
        self.test_webhook_test_utility()
        
        # Generate report
        success = self.generate_report()
        
        if success:
            print("\n🎉 All tests passed! System is ready for use.")
        else:
            print("\n⚠️  Some tests failed. Please fix the issues before using the system.")
        
        return success

if __name__ == "__main__":
    validator = SystemValidator()
    success = validator.run_validation()
    sys.exit(0 if success else 1)
