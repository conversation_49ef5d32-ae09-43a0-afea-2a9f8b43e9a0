#!/usr/bin/env python3
"""
GitHub Webhook Repository Setup Script
Creates and configures a GitHub repository for webhook testing
"""

import os
import sys
import subprocess
import json
import requests
from pathlib import Path

class WebhookRepoSetup:
    def __init__(self):
        self.repo_name = "github-webhook-mongodb"
        self.github_username = "nstotar"  # Update this with your GitHub username
        self.base_dir = Path(__file__).parent
        self.action_repo_dir = self.base_dir / "action-repo"
        
    def check_prerequisites(self):
        """Check if required tools are installed"""
        print("🔍 Checking prerequisites...")
        
        # Check Git
        try:
            subprocess.run(["git", "--version"], check=True, capture_output=True)
            print("✅ Git is installed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ Git is not installed or not in PATH")
            return False
            
        # Check GitHub CLI (optional)
        try:
            subprocess.run(["gh", "--version"], check=True, capture_output=True)
            print("✅ GitHub CLI is available")
            self.has_gh_cli = True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  GitHub CLI not found (optional)")
            self.has_gh_cli = False
            
        return True
    
    def setup_local_repo(self):
        """Setup local repository structure"""
        print("📁 Setting up local repository structure...")
        
        # Ensure action-repo directory exists
        if not self.action_repo_dir.exists():
            print("❌ action-repo directory not found")
            return False
            
        # Check if it's already a git repository
        if not (self.action_repo_dir / ".git").exists():
            print("🔧 Initializing git repository...")
            os.chdir(self.action_repo_dir)
            subprocess.run(["git", "init"], check=True)
            subprocess.run(["git", "branch", "-M", "main"], check=True)
        else:
            print("✅ Git repository already exists")
            
        return True
    
    def create_github_repo(self):
        """Create GitHub repository using GitHub CLI or manual instructions"""
        print("🚀 Setting up GitHub repository...")
        
        if self.has_gh_cli:
            try:
                os.chdir(self.action_repo_dir)
                # Check if repo already exists
                result = subprocess.run(
                    ["gh", "repo", "view", f"{self.github_username}/{self.repo_name}"],
                    capture_output=True
                )
                
                if result.returncode == 0:
                    print("✅ GitHub repository already exists")
                    return True
                    
                # Create new repository
                subprocess.run([
                    "gh", "repo", "create", self.repo_name,
                    "--public",
                    "--description", "GitHub Webhook MongoDB Integration System",
                    "--clone=false"
                ], check=True)
                
                # Add remote
                subprocess.run([
                    "git", "remote", "add", "origin",
                    f"https://github.com/{self.github_username}/{self.repo_name}.git"
                ], check=True)
                
                print("✅ GitHub repository created successfully")
                return True
                
            except subprocess.CalledProcessError as e:
                print(f"❌ Failed to create GitHub repository: {e}")
                return False
        else:
            print("📋 Manual GitHub repository setup required:")
            print(f"1. Go to https://github.com/new")
            print(f"2. Repository name: {self.repo_name}")
            print(f"3. Description: GitHub Webhook MongoDB Integration System")
            print(f"4. Make it public")
            print(f"5. Don't initialize with README (we have files already)")
            print(f"6. Click 'Create repository'")
            print(f"7. Run: git remote add origin https://github.com/{self.github_username}/{self.repo_name}.git")
            return True
    
    def setup_webhook_config(self):
        """Setup webhook configuration files"""
        print("⚙️ Setting up webhook configuration...")
        
        # Create .env file if it doesn't exist
        env_file = self.base_dir / ".env"
        if not env_file.exists():
            env_example = self.base_dir / ".env.example"
            if env_example.exists():
                import shutil
                shutil.copy(env_example, env_file)
                print("✅ Created .env file from .env.example")
            else:
                # Create basic .env file
                with open(env_file, 'w') as f:
                    f.write("""# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/github_webhook_db
MONGODB_DATABASE=github_webhook_db
MONGODB_COLLECTION=repository_data

# Flask Configuration
FLASK_PORT=5000
FLASK_HOST=0.0.0.0
FLASK_DEBUG=True

# GitHub Webhook Configuration
GITHUB_WEBHOOK_SECRET=your_webhook_secret_here
""")
                print("✅ Created basic .env file")
        
        return True
    
    def install_dependencies(self):
        """Install required dependencies"""
        print("📦 Installing dependencies...")
        
        # Install Python dependencies
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                         check=True, cwd=self.base_dir)
            print("✅ Python dependencies installed")
        except subprocess.CalledProcessError:
            print("❌ Failed to install Python dependencies")
            return False
            
        # Install Node.js dependencies for action-repo
        try:
            subprocess.run(["npm", "install"], check=True, cwd=self.action_repo_dir)
            print("✅ Node.js dependencies installed")
        except subprocess.CalledProcessError:
            print("❌ Failed to install Node.js dependencies")
            return False
            
        return True
    
    def test_system(self):
        """Test the webhook system"""
        print("🧪 Testing webhook system...")
        
        try:
            # Test MongoDB connection
            from database.connection import MongoDBConnection
            db = MongoDBConnection()
            if db.test_connection():
                print("✅ MongoDB connection successful")
            else:
                print("❌ MongoDB connection failed")
                return False
                
            print("✅ System test completed successfully")
            return True
            
        except Exception as e:
            print(f"❌ System test failed: {e}")
            return False
    
    def display_next_steps(self):
        """Display next steps for the user"""
        print("\n🎉 Setup completed successfully!")
        print("\n📋 Next Steps:")
        print("1. Start MongoDB: mongod")
        print("2. Start webhook receiver: python webhook_receiver.py")
        print("3. Start data display: python data_display.py")
        print("4. Test webhook: npm run test-webhook")
        print("\n🌐 Configure GitHub Webhook:")
        print(f"1. Go to https://github.com/{self.github_username}/{self.repo_name}/settings/hooks")
        print("2. Click 'Add webhook'")
        print("3. Payload URL: http://your-server:5000/webhook")
        print("4. Content type: application/json")
        print("5. Select 'Push' and 'Pull requests' events")
        print("6. Add webhook secret (optional)")
        print("\n🚀 Your webhook system is ready!")
    
    def run_setup(self):
        """Run the complete setup process"""
        print("🚀 GitHub Webhook Repository Setup")
        print("=" * 50)
        
        if not self.check_prerequisites():
            return False
            
        if not self.setup_local_repo():
            return False
            
        if not self.create_github_repo():
            return False
            
        if not self.setup_webhook_config():
            return False
            
        if not self.install_dependencies():
            return False
            
        if not self.test_system():
            return False
            
        self.display_next_steps()
        return True

if __name__ == "__main__":
    setup = WebhookRepoSetup()
    success = setup.run_setup()
    sys.exit(0 if success else 1)
