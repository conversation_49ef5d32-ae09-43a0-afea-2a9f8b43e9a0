{"name": "action-repo", "version": "1.0.0", "description": "GitHub Actions repository for webhook testing with MongoDB integration", "main": "index.js", "scripts": {"start": "node index.js", "test": "node webhook-test.js", "webhook-test": "node webhook-test.js", "webhook-test-pr": "node webhook-test.js http://localhost:5000/webhook pull_request"}, "keywords": ["github-actions", "webhook", "testing", "mongodb"], "author": "nstotar", "license": "MIT", "dependencies": {"axios": "^1.5.0"}}