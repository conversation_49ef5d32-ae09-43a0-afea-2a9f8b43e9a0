{"timestamp": "2025-07-06T19:03:06.575853+00:00", "summary": {"total": 27, "passed": 26, "failed": 1, "success_rate": 96.29629629629629}, "results": [{"test": "File exists: webhook_receiver.py", "success": true, "message": "", "timestamp": "2025-07-06T19:03:02.124337+00:00"}, {"test": "File exists: data_display.py", "success": true, "message": "", "timestamp": "2025-07-06T19:03:02.124337+00:00"}, {"test": "File exists: start_system.py", "success": true, "message": "", "timestamp": "2025-07-06T19:03:02.124337+00:00"}, {"test": "File exists: requirements.txt", "success": true, "message": "", "timestamp": "2025-07-06T19:03:02.124337+00:00"}, {"test": "File exists: package.json", "success": true, "message": "", "timestamp": "2025-07-06T19:03:02.124337+00:00"}, {"test": "File exists: .env.example", "success": true, "message": "", "timestamp": "2025-07-06T19:03:02.125381+00:00"}, {"test": "File exists: README.md", "success": true, "message": "", "timestamp": "2025-07-06T19:03:02.125381+00:00"}, {"test": "File exists: models/repository_data.py", "success": true, "message": "", "timestamp": "2025-07-06T19:03:02.125381+00:00"}, {"test": "File exists: database/connection.py", "success": true, "message": "", "timestamp": "2025-07-06T19:03:02.125381+00:00"}, {"test": "File exists: action-repo/package.json", "success": true, "message": "", "timestamp": "2025-07-06T19:03:02.125381+00:00"}, {"test": "File exists: action-repo/webhook-test.js", "success": true, "message": "", "timestamp": "2025-07-06T19:03:02.125381+00:00"}, {"test": "File exists: action-repo/.github/workflows/webhook-trigger.yml", "success": true, "message": "", "timestamp": "2025-07-06T19:03:02.125381+00:00"}, {"test": "Python module: flask", "success": true, "message": "", "timestamp": "2025-07-06T19:03:02.269268+00:00"}, {"test": "Python module: pymongo", "success": true, "message": "", "timestamp": "2025-07-06T19:03:02.393445+00:00"}, {"test": "Python module: python-dotenv", "success": true, "message": "", "timestamp": "2025-07-06T19:03:02.397516+00:00"}, {"test": "Python module: requests", "success": true, "message": "", "timestamp": "2025-07-06T19:03:02.397516+00:00"}, {"test": "MongoDB connection", "success": true, "message": "", "timestamp": "2025-07-06T19:03:02.430558+00:00"}, {"test": "MongoDB data insertion", "success": true, "message": "", "timestamp": "2025-07-06T19:03:02.464063+00:00"}, {"test": "Webhook receiver running", "success": false, "message": "HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C2EF14D7D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-06T19:03:06.563101+00:00"}, {"test": "Data display initialization", "success": true, "message": "", "timestamp": "2025-07-06T19:03:06.572692+00:00"}, {"test": "Data display database access", "success": true, "message": "", "timestamp": "2025-07-06T19:03:06.572692+00:00"}, {"test": "GitHub Actions workflow file", "success": true, "message": "", "timestamp": "2025-07-06T19:03:06.573725+00:00"}, {"test": "Workflow has push trigger", "success": true, "message": "", "timestamp": "2025-07-06T19:03:06.573725+00:00"}, {"test": "Workflow has PR trigger", "success": true, "message": "", "timestamp": "2025-07-06T19:03:06.573725+00:00"}, {"test": "Workflow has webhook test", "success": true, "message": "", "timestamp": "2025-07-06T19:03:06.573725+00:00"}, {"test": "Webhook test utility exists", "success": true, "message": "", "timestamp": "2025-07-06T19:03:06.574833+00:00"}, {"test": "Node.js dependencies installed", "success": true, "message": "", "timestamp": "2025-07-06T19:03:06.574833+00:00"}]}