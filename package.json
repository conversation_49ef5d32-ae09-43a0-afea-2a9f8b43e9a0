{"name": "github-webhook-mongodb", "version": "1.0.0", "description": "GitHub webhook system that integrates with MongoDB for repository data tracking", "main": "webhook_receiver.py", "scripts": {"start": "python webhook_receiver.py", "start-display": "python data_display.py", "start-system": "python start_system.py", "test-webhook": "node action-repo/webhook-test.js", "test": "jest"}, "keywords": ["github", "webhook", "mongodb", "repository", "tracking"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2"}}